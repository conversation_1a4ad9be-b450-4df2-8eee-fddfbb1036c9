<?php

namespace App\Services;

use Stripe\Stripe;
use Stripe\Customer;
use Stripe\PaymentMethod;
use Stripe\Exception\ApiErrorException;
use Illuminate\Support\Facades\Log;

class StripeService
{
    public function __construct()
    {
        // Set Stripe API key
        Stripe::setApiKey(env('STRIPE_SECRET_KEY'));
    }

    /**
     * Create a customer in Stripe
     *
     * @param array $customerData - Customer data
     * @return array - Response from Stripe API
     */
    public function createCustomer(array $customerData): array
    {
        try {
            $formattedData = $this->formatCustomerData($customerData);

            Log::info('Creating Stripe customer', [
                'formatted_data' => $formattedData,
                'original_data' => $customerData
            ]);

            $customer = Customer::create($formattedData);

            Log::info('Stripe customer created successfully', [
                'customer_id' => $customer->id,
                'customer_email' => $customer->email
            ]);

            return [
                'success' => true,
                'data' => [
                    'customer' => $customer->toArray(),
                    'customer_id' => $customer->id
                ],
                'status_code' => 200
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe API error while creating customer', [
                'error_message' => $e->getMessage(),
                'error_code' => $e->getStripeCode(),
                'customer_data' => $customerData
            ]);

            return [
                'success' => false,
                'error' => [
                    'message' => $e->getMessage(),
                    'code' => $e->getStripeCode()
                ],
                'status_code' => $e->getHttpStatus() ?? 400
            ];

        } catch (\Exception $e) {
            Log::error('General error while creating Stripe customer', [
                'error_message' => $e->getMessage(),
                'customer_data' => $customerData
            ]);

            return [
                'success' => false,
                'error' => ['message' => 'An error occurred while creating customer: ' . $e->getMessage()],
                'status_code' => 500
            ];
        }
    }

    /**
     * Update a customer in Stripe
     *
     * @param string $customerId - Stripe customer ID
     * @param array $customerData - Customer data to update
     * @return array - Response from Stripe API
     */
    public function updateCustomer(string $customerId, array $customerData): array
    {
        try {
            $formattedData = $this->formatCustomerData($customerData);

            Log::info('Updating Stripe customer', [
                'customer_id' => $customerId,
                'formatted_data' => $formattedData
            ]);

            $customer = Customer::update($customerId, $formattedData);

            Log::info('Stripe customer updated successfully', [
                'customer_id' => $customer->id
            ]);

            return [
                'success' => true,
                'data' => [
                    'customer' => $customer->toArray(),
                    'customer_id' => $customer->id
                ],
                'status_code' => 200
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe API error while updating customer', [
                'customer_id' => $customerId,
                'error_message' => $e->getMessage(),
                'error_code' => $e->getStripeCode()
            ]);

            return [
                'success' => false,
                'error' => [
                    'message' => $e->getMessage(),
                    'code' => $e->getStripeCode()
                ],
                'status_code' => $e->getHttpStatus() ?? 400
            ];

        } catch (\Exception $e) {
            Log::error('General error while updating Stripe customer', [
                'customer_id' => $customerId,
                'error_message' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => ['message' => 'An error occurred while updating customer: ' . $e->getMessage()],
                'status_code' => 500
            ];
        }
    }

    /**
     * Get a customer from Stripe
     *
     * @param string $customerId - Stripe customer ID
     * @return array - Response from Stripe API
     */
    public function getCustomer(string $customerId): array
    {
        try {
            $customer = Customer::retrieve($customerId);

            return [
                'success' => true,
                'data' => [
                    'customer' => $customer->toArray(),
                    'customer_id' => $customer->id
                ],
                'status_code' => 200
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe API error while retrieving customer', [
                'customer_id' => $customerId,
                'error_message' => $e->getMessage(),
                'error_code' => $e->getStripeCode()
            ]);

            return [
                'success' => false,
                'error' => [
                    'message' => $e->getMessage(),
                    'code' => $e->getStripeCode()
                ],
                'status_code' => $e->getHttpStatus() ?? 400
            ];

        } catch (\Exception $e) {
            Log::error('General error while retrieving Stripe customer', [
                'customer_id' => $customerId,
                'error_message' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => ['message' => 'An error occurred while retrieving customer: ' . $e->getMessage()],
                'status_code' => 500
            ];
        }
    }

    /**
     * Delete a customer from Stripe
     *
     * @param string $customerId - Stripe customer ID
     * @return array - Response from Stripe API
     */
    public function deleteCustomer(string $customerId): array
    {
        try {
            $customer = Customer::retrieve($customerId);
            $deletedCustomer = $customer->delete();

            Log::info('Stripe customer deleted successfully', [
                'customer_id' => $customerId
            ]);

            return [
                'success' => true,
                'data' => [
                    'deleted' => $deletedCustomer->deleted,
                    'customer_id' => $deletedCustomer->id
                ],
                'status_code' => 200
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe API error while deleting customer', [
                'customer_id' => $customerId,
                'error_message' => $e->getMessage(),
                'error_code' => $e->getStripeCode()
            ]);

            return [
                'success' => false,
                'error' => [
                    'message' => $e->getMessage(),
                    'code' => $e->getStripeCode()
                ],
                'status_code' => $e->getHttpStatus() ?? 400
            ];

        } catch (\Exception $e) {
            Log::error('General error while deleting Stripe customer', [
                'customer_id' => $customerId,
                'error_message' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => ['message' => 'An error occurred while deleting customer: ' . $e->getMessage()],
                'status_code' => 500
            ];
        }
    }

    /**
     * Format customer data for Stripe API
     *
     * @param array $data - Raw customer data
     * @return array - Formatted data for Stripe API
     */
    private function formatCustomerData(array $data): array
    {
        $formattedData = [
            'name' => trim(($data['firstname'] ?? '') . ' ' . ($data['lastname'] ?? '')),
            'email' => $data['email'] ?? '',
            'phone' => $data['phone_number'] ?? $data['phone'] ?? '',
            'description' => $data['account_name'] ?? $data['company'] ?? '',
        ];

        // Add address if provided
        if (!empty($data['street_address']) || !empty($data['address_1']) || !empty($data['city'])) {
            $formattedData['address'] = [
                'line1' => $data['street_address'] ?? $data['address_1'] ?? '',
                'line2' => $data['address_2'] ?? '',
                'city' => $data['city'] ?? '',
                'state' => $data['state'] ?? '',
                'postal_code' => $data['zip_code'] ?? $data['postal_code'] ?? '',
                'country' => $this->convertToTwoCharCountryCode($data['country'] ?? 'US'),
            ];
        }

        // Add metadata
        $formattedData['metadata'] = [
            'source' => 'Henry Griffitts',
            'company' => $data['account_name'] ?? $data['company'] ?? '',
            'reference' => $data['reference'] ?? 'Henry'
        ];

        // Remove empty values
        return array_filter($formattedData, function($value) {
            return !empty($value) || $value === 0;
        });
    }

    /**
     * Convert country code/name to 2-character ISO code for Stripe API
     *
     * @param string $country
     * @return string
     */
    private function convertToTwoCharCountryCode($country): string
    {
        if (empty($country)) {
            return 'US'; // Default to US
        }

        // Convert to uppercase for comparison
        $country = strtoupper(trim($country));

        // Common country code mappings
        $countryMappings = [
            'USA' => 'US',
            'UNITED STATES' => 'US',
            'UNITED STATES OF AMERICA' => 'US',
            'CAN' => 'CA',
            'CANADA' => 'CA',
            'GBR' => 'GB',
            'UNITED KINGDOM' => 'GB',
            'UK' => 'GB',
            'AUS' => 'AU',
            'AUSTRALIA' => 'AU',
        ];

        // Check if it's already a 2-character code
        if (strlen($country) === 2) {
            return $country;
        }

        // Check mappings
        if (isset($countryMappings[$country])) {
            return $countryMappings[$country];
        }

        // Default to US if not found
        return 'US';
    }

    /**
     * Add a payment method to a customer in Stripe
     *
     * @param string $customerId - Stripe customer ID
     * @param array $paymentData - Payment method data
     * @return array - Response from Stripe API
     */
    public function addPaymentMethod(string $customerId, array $paymentData): array
    {
        try {
            // Create payment method first
            $paymentMethodData = $this->formatPaymentData($paymentData);

            Log::info('Creating Stripe payment method', [
                'customer_id' => $customerId,
                'formatted_data' => $paymentMethodData,
                'original_data' => $paymentData
            ]);

            $paymentMethod = PaymentMethod::create($paymentMethodData);

            // Attach payment method to customer
            $paymentMethod->attach(['customer' => $customerId]);

            Log::info('Stripe payment method created and attached successfully', [
                'customer_id' => $customerId,
                'payment_method_id' => $paymentMethod->id
            ]);

            return [
                'success' => true,
                'data' => [
                    'payment_method' => $paymentMethod->toArray(),
                    'payment_method_id' => $paymentMethod->id
                ],
                'status_code' => 200
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe API error while adding payment method', [
                'customer_id' => $customerId,
                'error_message' => $e->getMessage(),
                'error_code' => $e->getStripeCode(),
                'payment_data' => $paymentData
            ]);

            return [
                'success' => false,
                'error' => [
                    'message' => $e->getMessage(),
                    'code' => $e->getStripeCode()
                ],
                'status_code' => $e->getHttpStatus() ?? 400
            ];

        } catch (\Exception $e) {
            Log::error('General error while adding Stripe payment method', [
                'customer_id' => $customerId,
                'error_message' => $e->getMessage(),
                'payment_data' => $paymentData
            ]);

            return [
                'success' => false,
                'error' => ['message' => 'An error occurred while adding payment method: ' . $e->getMessage()],
                'status_code' => 500
            ];
        }
    }

    /**
     * Get payment methods for a customer from Stripe
     *
     * @param string $customerId - Stripe customer ID
     * @return array - Response from Stripe API
     */
    public function getPaymentMethods(string $customerId): array
    {
        try {
            $paymentMethods = PaymentMethod::all([
                'customer' => $customerId,
                'type' => 'card',
            ]);

            Log::info('Retrieved Stripe payment methods successfully', [
                'customer_id' => $customerId,
                'count' => count($paymentMethods->data)
            ]);

            return [
                'success' => true,
                'data' => $paymentMethods->data, // Return payment methods directly
                'count' => count($paymentMethods->data),
                'status_code' => 200
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe API error while retrieving payment methods', [
                'customer_id' => $customerId,
                'error_message' => $e->getMessage(),
                'error_code' => $e->getStripeCode()
            ]);

            return [
                'success' => false,
                'error' => [
                    'message' => $e->getMessage(),
                    'code' => $e->getStripeCode()
                ],
                'status_code' => $e->getHttpStatus() ?? 400
            ];

        } catch (\Exception $e) {
            Log::error('General error while retrieving Stripe payment methods', [
                'customer_id' => $customerId,
                'error_message' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => ['message' => 'An error occurred while retrieving payment methods: ' . $e->getMessage()],
                'status_code' => 500
            ];
        }
    }

    /**
     * Delete a payment method from Stripe
     *
     * @param string $customerId - Stripe customer ID (for logging purposes)
     * @param string $paymentMethodId - Stripe payment method ID
     * @return array - Response from Stripe API
     */
    public function deletePaymentMethod(string $customerId, string $paymentMethodId): array
    {
        try {
            $paymentMethod = PaymentMethod::retrieve($paymentMethodId);
            $deletedPaymentMethod = $paymentMethod->detach();

            Log::info('Stripe payment method deleted successfully', [
                'customer_id' => $customerId,
                'payment_method_id' => $paymentMethodId
            ]);

            return [
                'success' => true,
                'data' => [
                    'payment_method' => $deletedPaymentMethod->toArray(),
                    'payment_method_id' => $paymentMethodId
                ],
                'status_code' => 200
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe API error while deleting payment method', [
                'customer_id' => $customerId,
                'payment_method_id' => $paymentMethodId,
                'error_message' => $e->getMessage(),
                'error_code' => $e->getStripeCode()
            ]);

            return [
                'success' => false,
                'error' => [
                    'message' => $e->getMessage(),
                    'code' => $e->getStripeCode()
                ],
                'status_code' => $e->getHttpStatus() ?? 400
            ];

        } catch (\Exception $e) {
            Log::error('General error while deleting Stripe payment method', [
                'customer_id' => $customerId,
                'payment_method_id' => $paymentMethodId,
                'error_message' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => ['message' => 'An error occurred while deleting payment method: ' . $e->getMessage()],
                'status_code' => 500
            ];
        }
    }

    /**
     * Format payment data for Stripe API
     *
     * @param array $data - Raw payment data
     * @return array - Formatted data for Stripe API
     */
    private function formatPaymentData(array $data): array
    {
        // Remove spaces from card number
        $cardNumber = preg_replace('/\s+/', '', $data['card_number'] ?? '');

        // Format expiry date as MM/YY for Stripe
        $cardExp = $this->formatExpiryForStripe($data['card_expiry'] ?? '');

        return [
            'type' => 'card',
            'card' => [
                'number' => $cardNumber,
                'exp_month' => $cardExp['month'],
                'exp_year' => $cardExp['year'],
                'cvc' => $data['card_cvv_number'] ?? $data['card_cvv'] ?? '',
            ],
            'billing_details' => [
                'name' => $data['card_holder_name'] ?? '',
            ],
        ];
    }

    /**
     * Format expiry date for Stripe API
     *
     * @param string $expiry - Expiry date (MM/YY format from form)
     * @return array - Month and year for Stripe
     */
    private function formatExpiryForStripe(string $expiry): array
    {
        if (empty($expiry)) {
            return ['month' => '', 'year' => ''];
        }

        // Remove any spaces and split by /
        $parts = explode('/', trim($expiry));

        if (count($parts) !== 2) {
            return ['month' => '', 'year' => ''];
        }

        $month = (int) str_pad(trim($parts[0]), 2, '0', STR_PAD_LEFT);
        $year = trim($parts[1]);

        // Ensure we have 4-digit year for Stripe
        if (strlen($year) === 2) {
            $year = '20' . $year; // Convert 25 to 2025
        }

        return [
            'month' => $month,
            'year' => (int) $year
        ];
    }

    /**
     * Validate payment method data
     *
     * @param array $paymentData
     * @return array
     */
    public function validatePaymentData(array $paymentData): array
    {
        $errors = [];

        // Required fields validation
        if (empty($paymentData['card_number'])) {
            $errors[] = 'Card number is required';
        }

        if (empty($paymentData['card_expiry'])) {
            $errors[] = 'Card expiry is required';
        }

        if (empty($paymentData['card_cvv_number']) && empty($paymentData['card_cvv'])) {
            $errors[] = 'Card CVV is required';
        }

        if (empty($paymentData['card_holder_name'])) {
            $errors[] = 'Card holder name is required';
        }

        // Card number validation (basic)
        $cardNumber = preg_replace('/\s+/', '', $paymentData['card_number'] ?? '');
        if (!empty($cardNumber) && !preg_match('/^\d{13,19}$/', $cardNumber)) {
            $errors[] = 'Invalid card number format';
        }

        // Expiry validation
        if (!empty($paymentData['card_expiry'])) {
            $expiry = $this->formatExpiryForStripe($paymentData['card_expiry']);
            if (empty($expiry['month']) || empty($expiry['year'])) {
                $errors[] = 'Invalid expiry date format (use MM/YY)';
            } else {
                // Check if card is expired
                $currentYear = (int) date('Y');
                $currentMonth = (int) date('m');

                if ($expiry['year'] < $currentYear ||
                    ($expiry['year'] == $currentYear && $expiry['month'] < $currentMonth)) {
                    $errors[] = 'Card has expired';
                }
            }
        }

        // CVV validation
        $cvv = $paymentData['card_cvv_number'] ?? $paymentData['card_cvv'] ?? '';
        if (!empty($cvv) && !preg_match('/^\d{3,4}$/', $cvv)) {
            $errors[] = 'Invalid CVV format';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
