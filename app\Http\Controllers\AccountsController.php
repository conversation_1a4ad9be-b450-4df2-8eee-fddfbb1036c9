<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\AcctMgr;
use App\Traits\StaxTrait;
use App\Traits\StripeTrait;

use App\Models\Acct;
use App\Http\Requests\AccountRequest;
use App\Models\AcctType;
use App\Models\ActStat;
use App\Models\Cntry;
use App\Models\Ftr;
use App\Models\ShipVia;
use Spatie\Permission\Models\Permission;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;


class AccountsController extends Controller
{
    use StaxTrait, StripeTrait;

    public function __construct()
    {
        // Add middleware here if needed
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $actQuery = Acct::with('status', 'country')->orderByDesc('AcctId');
            if ($request->has('search') && $request->search['value'] != '') {
                $searchValue = $request->search['value'];
                $actQuery->where(function ($query) use ($searchValue) {
                    $query->where('AcctNm', 'like', "%$searchValue%")
                        ->orWhere('AcctId', 'like', "%$searchValue%")
                        ->orWhereHas('country', function ($q) use ($searchValue) {
                            $q->where('CntryNm', 'like', "%$searchValue%");
                        })
                        ->orWhereHas('status', function ($q) use ($searchValue) {
                            $q->where('AcctStatDsc', 'like', "%$searchValue%");
                        })
                        ->orWhereRaw("DATE_FORMAT(updated_at, '%d/%m/%Y') like ?", ["%$searchValue%"]);
                });
            }
            if ($request->has('country') && $request->country != '') {
                $actQuery->whereHas('country', function ($query) use ($request) {
                    $query->where('CntryNm', 'like', "%{$request->country}%");
                });
            }
            if ($request->has('lastChanged') && $request->lastChanged != '') {
                $actQuery->whereDate('updated_at', '=', $request->lastChanged);
            }
            return DataTables::of($actQuery)
                ->addColumn('name', function ($row) {
                    return $row->AcctNm ?? 'N/A';
                })
                ->addColumn('country', function ($row) {
                    return $row->country->CntryNm ?? 'N/A';
                })
                ->addColumn('sales_type', function ($row) {
                    return  $row->type->AcctTypeDsc ?? 'N/A';
                })
                ->addColumn('account_manager', function ($row) {
                    return $row->accountManager->AcctMgrNm ?? 'N/A';
                })
                ->addColumn('last_changed', function ($row) {
                    return $row->updated_at->format('d/m/Y') ?? 'N/A';
                })
                ->addColumn('status', function ($row) {
                    return $row->status->AcctStatDsc;
                })
                ->addColumn('action', function ($row) {
                    return '
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                            <img class="ellipsis_img" src="' . asset('website') . '/assets/images/ellipsis.svg">
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                            <li><a class="dropdown-item">View</a></li>
                            <li><a class="dropdown-item edit_account_button" account_id="' . $row->AcctId . '">Edit</a></li>
                            <li>
                                <form action="' . route('accounts.destroy', $row->AcctId) . '" method="POST" class="delete-form">
                                    ' . csrf_field() . '
                                    ' . method_field('DELETE') . '
                                    <a class="dropdown-item" href="javascript:void(0)" onclick="showDeleteConfirmation(this)">Delete</a>
                                </form>
                            </li>
                            <li><a class="dropdown-item payments_modal" data-bs-toggle="modal" data-bs-target="#payment-method" maunf_id="'. $row->ManufLocID .'" account_id="' . $row->AcctId . '">Payment Methods</a></li>
                        </ul>
                    </div>
                ';
                })
                ->rawColumns(['status', 'action', 'account_manager'])
                ->make(true);
        }


        $countries = Cntry::where('CurrInd',1)->get();
        $allStatus = ActStat::where('CurrInd',1)->get();
        $acctTypes = AcctType::where('CurrInd',1)->get();
        $accountManagers = AcctMgr::where('CurrInd',1)->get();
        $shipVias = ShipVia::where('CurrInd',1)->get();

        return view('dashboard.AdminDashboard.accounts_golf_course', compact('countries', 'allStatus', 'acctTypes', 'shipVias','accountManagers'));
    }

    public function create()
    {
        return view('accounts.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'account_name' => 'required',
            'manufLoc' => 'required',
            'country' => 'required',
            'status' => 'required',
            'acctType' => 'required',
            'website' => 'required',
            'shipVia' => 'required',
            'comment' => 'required',
        ]);

        // Create account only (no Stax integration)
        $account = Acct::updateOrCreate([
            'AcctNm' => $request->account_name,
        ], [
            'ManufLocID' => $request->manufLoc,
            'CntryCd' => $request->country,
            'AcctURL' => $request->website,
            'AcctStatId' => $request->status,
            'AcctTypeId' => $request->acctType,
            'AcctMgrId' => $request->acctMgrId,
            'ShipViaId' => $request->shipVia,
            'Comm' => $request->comment,
            'location' => $request->location,
            'phoneNum' => $request->phone ?? null,
        ]);

        return redirect(url('accounts'))->with([
            'title' => 'Done',
            'message' => 'Account created successfully',
            'type' => 'success'
        ]);
    }

    public function show($id)
    {
        $account = Acct::findOrFail($id);
        return view('accounts.show', compact('account'));
    }

    public function edit($id)
    {
        $account = Acct::findOrFail($id);
        $countries = Cntry::where('CurrInd',1)->get();
        $allStatus = ActStat::where('CurrInd',1)->get();
        $acctTypes = AcctType::where('CurrInd',1)->get();
        $accountManagers = AcctMgr::where('CurrInd',1)->get();
        $shipVias = ShipVia::where('CurrInd',1)->get();
        return view('dashboard.AdminDashboard.account_edit_modal', compact('account', 'allStatus', 'countries', 'acctTypes', 'shipVias','accountManagers'));
    }

    public function update(Request $request, $id)
    {
        $account = Acct::find($id);
        $account->update([
//            'ManufLocID' => $request->manufLoc,
            'AcctNm' => $request->acount_name ?? null,
            'CntryCd' => $request->country ?? null,
            'AcctStatId' => $request->status ?? null,
            'AcctTypeId' => $request->acctType ?? null,
            'AcctMgrId' => $request->acctMgrId ?? null,
            'AcctURL' => $request->website ?? null,
            'ShipViaId' => $request->shipVia ?? null,
            'PhoneNum' => $request->phone ?? null,
            'AcctUrl' => $request->website ?? null,
            'location' => $request->location ?? null,
            'Comm' => $request->notes ?? null,
        ]);

        return redirect(url('accounts'))->with(['title' => 'Done', 'message' => 'Account updated successfully', 'type' => 'success']);
    }

    public function destroy($id)
    {
        $account = Acct::findOrFail($id);
        $account->delete();

        return redirect(url('accounts'))->with(['title' => 'Done', 'message' => 'Account deleted successfully', 'type' => 'success']);
    }

    /**
     * Get payment methods for an account
     */
    public function getPaymentMethods(Request $request)
    {
        $accountId = $request->account_id;
        $account = Acct::findOrFail($accountId);
        if($request->gateway == "Stax"){
            if (empty($account->StaxCustomerId)) {
                return response()->json([
                    'success' => false,
                    'error' => 'No Stax customer ID found for this account'
                ], 404);
            }

            $result = $this->getStaxPaymentMethods($account->StaxCustomerId);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'data' => $result['data'],
                    'account' => $account
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => $result['error']
                ], $result['status_code']);
            }
        }else if($request->gateway == "Stripe"){
            if (empty($account->StripeCustomerId)) {
                return response()->json([
                    'success' => false,
                    'error' => 'No Stripe customer ID found for this account'
                ], 404);
            }

            $result = $this->getStripePaymentMethods($account->StripeCustomerId);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'data' => $result['data'],
                    'account' => $account
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => $result['error']
                ], $result['status_code']);
            }
        }
    }

    /**
     * Add payment method to an account
     */
    public function addPaymentMethod(Request $request)
    {
        $request->validate([
            'account_id' => 'required',
            'card_number' => 'required|string',
            'card_holder_name' => 'required|string',
            'card_cvv_number' => 'required|string|min:3|max:4',
            'card_expiry' => 'required|string',
            'gateway' => 'required|string|in:Stax,Stripe'
        ]);

        $account = Acct::findOrFail($request->account_id);

        if ($request->gateway == 'Stripe') {
            if (empty($account->StripeCustomerId)) {
                return response()->json([
                    'success' => false,
                    'error' => 'No Stripe customer ID found for this account'
                ], 404);
            }
            $request['customer_id'] = $account->StripeCustomerId;
            return $this->handleStripePaymentMethodAddition($request);
        } else {
            // Stax logic (existing)
            if (empty($account->StaxCustomerId)) {
                return redirect(url('accounts'))->with([
                    'title' => 'Error',
                    'message' => 'No Stax customer ID found for this account',
                    'type' => 'error'
                ]);
            }
            $validation = $this->validateStaxPaymentData($request->all());
            if (!$validation['valid']) {
                return redirect(url('accounts'))->with([
                    'title' => 'Error',
                    'message' => $validation['message'],
                    'type' => 'error'
                ]);
            }

            // Add payment method to Stax
            $result = $this->addStaxPaymentMethod($account->StaxCustomerId, $request->all());

            if ($result['success']) {
                return redirect(url('accounts'))->with([
                    'title' => 'Done',
                    'message' => 'Payment method added successfully',
                    'type' => 'success'
                ]);
            } else {
                // Better error handling to see what's actually happening
                $errorMessage = 'Unknown error';
                if (isset($result['error'])) {
                    if (is_array($result['error'])) {
                        $errorMessage = $result['error']['message'] ?? json_encode($result['error']);
                    } else {
                        $errorMessage = $result['error'];
                    }
                }
                return redirect(url('accounts'))->with([
                    'title' => 'Error',
                    'message' => 'Failed to add payment method: ' . $errorMessage,
                    'type' => 'error'
                ]);
            }
        }
    }

    /**
     * Delete payment method from an account
     */
    public function deletePaymentMethod(Request $request)
    {
        $request->validate([
            'account_id' => 'required',
            'payment_method_id' => 'required',
            'gateway' => 'required|string|in:Stax,Stripe'
        ]);

        $account = Acct::findOrFail($request->account_id);

        if ($request->gateway == 'Stripe') {
            if (empty($account->StripeCustomerId)) {
                return response()->json([
                    'success' => false,
                    'error' => 'No Stripe customer ID found for this account'
                ], 404);
            }

            // Use the trait method to delete from Stripe
            $result = $this->deleteStripePaymentMethod($account->StripeCustomerId, $request->payment_method_id);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Payment method deleted successfully',
                    'data' => $result['data']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => $result['error']
                ], $result['status_code']);
            }
        } else {
            // Stax logic (existing)
            if (empty($account->StaxCustomerId)) {
                return response()->json([
                    'success' => false,
                    'error' => 'No Stax customer ID found for this account'
                ], 404);
            }

            $result = $this->deleteStaxPaymentMethod($account->StaxCustomerId, $request->payment_method_id);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Payment method deleted successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => $result['error']
                ], $result['status_code']);
            }
        }
    }

    /**
     * Check if account has Stax customer ID
     */
    public function checkCustomer(Request $request)
    {
        $accountId = $request->account_id;
        $account = Acct::findOrFail($accountId);
        if (isset($account->ManufLocID) && ($account->ManufLocID == "3" || $account->ManufLocID == "4")) {
            return response()->json([
                'has_stripe_customer' => !empty($account->StripeCustomerId),
                'stripe_customer_id' => $account->StripeCustomerId,
                'account_name' => $account->AcctNm,
                'gateway' => 'Stripe'
            ]);
        } else {
            return response()->json([
                'has_stax_customer' => !empty($account->StaxCustomerId),
                'stax_customer_id' => $account->StaxCustomerId,
                'account_name' => $account->AcctNm,
                'gateway' => 'Stax'
            ]);
        }
    }

    /**
     * Create Stax customer for existing account
     */
    public function createStaxCustomerOnly(Request $request)
    {
        $request->validate([
            'account_id' => 'required',
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'email' => 'required|email',
        ]);
        $account = Acct::findOrFail($request->account_id);
        $staxResult = $this->createAccountWithStaxCustomer($request, $account);
        if ($staxResult['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Stax customer created successfully',
                'stax_customer' => $staxResult['stax_customer']
            ]);
        } else {
            // Better error handling
            $errorMessage = 'Unknown error';

            if (isset($staxResult['stax_error'])) {
                if (is_array($staxResult['stax_error'])) {
                    if (isset($staxResult['stax_error']['message'])) {
                        $errorMessage = $staxResult['stax_error']['message'];
                    } else {
                        $errorMessage = json_encode($staxResult['stax_error']);
                    }
                } else {
                    $errorMessage = $staxResult['stax_error'];
                }
            } else if (isset($staxResult['message'])) {
                $errorMessage = $staxResult['message'];
            }

            \Log::error('Failed to create Stax customer from payment modal', [
                'account_id' => $request->account_id,
                'error_message' => $errorMessage,
                'full_result' => $staxResult
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create Stax customer: ' . $errorMessage
            ], 422);
        }
    }
    public function createStripeCustomerOnly(Request $request)
    {
        $request->validate([
            'account_id' => 'required',
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'email' => 'required|email',
        ]);

        $account = Acct::findOrFail($request->account_id);
        $stripeResult = $this->createAccountWithStripeCustomer($request, $account);

        if ($stripeResult['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Stripe customer created successfully',
                'stripe_customer' => $stripeResult['stripe_customer']
            ]);
        } else {
            // Better error handling
            $errorMessage = 'Unknown error';

            if (isset($stripeResult['stripe_error'])) {
                if (is_array($stripeResult['stripe_error'])) {
                    if (isset($stripeResult['stripe_error']['message'])) {
                        $errorMessage = $stripeResult['stripe_error']['message'];
                    } else {
                        $errorMessage = json_encode($stripeResult['stripe_error']);
                    }
                } else {
                    $errorMessage = $stripeResult['stripe_error'];
                }
            } else if (isset($stripeResult['message'])) {
                $errorMessage = $stripeResult['message'];
            }

            \Log::error('Failed to create Stripe customer from payment modal', [
                'account_id' => $request->account_id,
                'error_message' => $errorMessage,
                'full_result' => $stripeResult
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create Stripe customer: ' . $errorMessage
            ], 422);
        }
    }
}
