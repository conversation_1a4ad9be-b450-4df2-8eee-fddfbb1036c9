<?php

namespace App\Traits;

use App\Services\StripeService;

trait StripeTrait
{
    /**
     * Get Stripe service instance
     *
     * @return StripeService
     */
    protected function getStripeService(): StripeService
    {
        return new StripeService();
    }

    /**
     * Create a customer in Stripe
     *
     * @param array $customerData
     * @return array
     */
    protected function createStripeCustomer(array $customerData): array
    {
        return $this->getStripeService()->createCustomer($customerData);
    }

    /**
     * Update a customer in Stripe
     *
     * @param string $customerId
     * @param array $customerData
     * @return array
     */
    protected function updateStripeCustomer(string $customerId, array $customerData): array
    {
        return $this->getStripeService()->updateCustomer($customerId, $customerData);
    }

    /**
     * Get a customer from Stripe
     *
     * @param string $customerId
     * @return array
     */
    protected function getStripeCustomer(string $customerId): array
    {
        return $this->getStripeService()->getCustomer($customerId);
    }

    /**
     * Delete a customer from Stripe
     *
     * @param string $customerId
     * @return array
     */
    protected function deleteStripeCustomer(string $customerId): array
    {
        return $this->getStripeService()->deleteCustomer($customerId);
    }

    /**
     * Validate customer data for Stripe
     *
     * @param array $customerData
     * @return array
     */
    protected function validateStripeCustomerData(array $customerData): array
    {
        // Basic validation for Stripe customer data
        $errors = [];

        if (empty($customerData['email'])) {
            $errors[] = 'Email is required';
        } elseif (!filter_var($customerData['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Invalid email format';
        }

        if (empty($customerData['firstname']) && empty($customerData['lastname'])) {
            $errors[] = 'First name or last name is required';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Add payment method to Stripe customer
     *
     * @param string $customerId
     * @param array $paymentData
     * @return array
     */
    protected function addStripePaymentMethod(string $customerId, array $paymentData): array
    {
        return $this->getStripeService()->addPaymentMethod($customerId, $paymentData);
    }

    /**
     * Get payment methods for Stripe customer
     *
     * @param string $customerId
     * @return array
     */
    protected function getStripePaymentMethods(string $customerId): array
    {
        return $this->getStripeService()->getPaymentMethods($customerId);
    }

    /**
     * Delete payment method from Stripe
     *
     * @param string $customerId
     * @param string $paymentMethodId
     * @return array
     */
    protected function deleteStripePaymentMethod(string $customerId, string $paymentMethodId): array
    {
        return $this->getStripeService()->deletePaymentMethod($customerId, $paymentMethodId);
    }

    /**
     * Validate payment method data
     *
     * @param array $paymentData
     * @return array
     */
    protected function validateStripePaymentData(array $paymentData): array
    {
        return $this->getStripeService()->validatePaymentData($paymentData);
    }

    /**
     * Create account and Stripe customer together
     *
     * @param \Illuminate\Http\Request $request
     * @param object $account - The created account object
     * @return array
     */
    protected function createAccountWithStripeCustomer($request, $account): array
    {
        $customerData = [
            'firstname' => $request->first_name,
            'lastname' => $request->last_name,
            'company' => $request->account_name,
            'email' => $request->email,
            'phone' => preg_replace('/\D/', '', $request->phone ?? ''),
            'street_address' => $request->location,
            'address_1' => $request->location,
            'city' => $request->city,
            'state' => $request->state,
            'zip_code' => $request->zip_code,
            'country' => $request->country,
            'reference' => 'Henry'
        ];
        // Validate customer data
        $validation = $this->validateStripeCustomerData($customerData);
        if (!$validation['valid']) {
            return [
                'success' => false,
                'message' => 'Validation failed',
                'stripe_error' => $validation['errors']
            ];
        }

        // Create Stripe customer
        $result = $this->createStripeCustomer($customerData);

        if ($result['success']) {
            // Update account with Stripe customer ID
            $account->StripeCustomerId = $result['data']['customer_id'];
            $account->save();

            return [
                'success' => true,
                'stripe_customer' => $result['data']['customer'],
                'customer_id' => $result['data']['customer_id']
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to create Stripe customer',
                'stripe_error' => $result['error']
            ];
        }
    }

    /**
     * Handle payment method addition via AJAX
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleStripePaymentMethodAddition($request)
    {
        $request->validate([
            'customer_id' => 'required',
            'card_number' => 'required',
            'card_expiry' => 'required',
            'card_cvv_number' => 'required',
            'card_holder_name' => 'required',
        ]);

        $customerId = $request->customer_id;
        $paymentData = [
            'card_number' => $request->card_number,
            'card_expiry' => $request->card_expiry,
            'card_cvv_number' => $request->card_cvv_number,
            'card_holder_name' => $request->card_holder_name,
        ];

        // Validate payment data
        $validation = $this->validateStripePaymentData($paymentData);
        if (!$validation['valid']) {
            return response()->json([
                'success' => false,
                'error' => $validation['errors']
            ], 422);
        }

        // Add payment method to Stripe
        $result = $this->addStripePaymentMethod($customerId, $paymentData);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'data' => $result['data']
            ], $result['status_code']);
        } else {
            return response()->json([
                'success' => false,
                'error' => $result['error']
            ], $result['status_code']);
        }
    }

    /**
     * Convert country code/name to 2-character ISO code for Stripe API
     *
     * @param string $country
     * @return string
     */
    private function convertToTwoCharCountryCode($country): string
    {
        if (empty($country)) {
            return 'US'; // Default to US
        }

        // Convert to uppercase for comparison
        $country = strtoupper(trim($country));

        // Common country code mappings
        $countryMappings = [
            'USA' => 'US',
            'UNITED STATES' => 'US',
            'UNITED STATES OF AMERICA' => 'US',
            'CAN' => 'CA',
            'CANADA' => 'CA',
            'GBR' => 'GB',
            'UNITED KINGDOM' => 'GB',
            'UK' => 'GB',
            'AUS' => 'AU',
            'AUSTRALIA' => 'AU',
        ];

        // Check if it's already a 2-character code
        if (strlen($country) === 2) {
            return $country;
        }

        // Check mappings
        if (isset($countryMappings[$country])) {
            return $countryMappings[$country];
        }

        // Default to US if not found
        return 'US';
    }
}
