@extends('theme.layout.master')

@push('css')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/css/intlTelInput.css"/>
    <style>
        .pac-container {
            z-index: 9999;
        }

        .iti.iti--allow-dropdown {
            width: 100%;
        }
    </style>
@endpush

@section('content')
    <section class="main-dashboard">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <div class="table_header">
                            <div class="user_engagement">
                                <h5>Accounts/Golf Course</h5>
                            </div>
                            <div class="side_fields custom_entries">
                                <div class="custom_action_fields">
                                    <div class="custom_search_box">
                                        <form>
                                            <div class="txt_field">
                                                <i class="fa-solid fa-magnifying-glass"></i>
                                                <input type="search" placeholder="Search"
                                                       class="form-control searchinput">
                                            </div>
                                        </form>
                                    </div>
                                    <!-- Filter Dropdown -->
                                    <div class="dropdown-btn">
                                        <button type="button" class="btn dropdown-toggle light_green_btn"
                                                id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="fas fa-filter"></i> Filter
                                        </button>
                                        <div class="dropdown-menu filter-dropdown" aria-labelledby="filterDropdown">
                                            <div class="dropdown_top">
                                                <h6 class="">Filter</h6>
                                                <button type="button" class="btn_close" data-bs-dismiss="dropdown"
                                                        aria-label="Close">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                            <form class="filter-form">
                                                <div class="form-group">
                                                    <label for="country">Country</label>
                                                    <input id="country" type="text" class="form-control" name="country"
                                                           placeholder="Enter City">
                                                </div>
                                                <div class="form-group">
                                                    <label for="sales-type">Sales Type</label>
                                                    <input id="sales-type" type="text" class="form-control"
                                                           name="sales_type" placeholder="Enter Sales Type">
                                                </div>
                                                <div class="form-group">
                                                    <label for="last-changed">Last Changed At</label>
                                                    <input id="last-changed" type="date" class="form-control"
                                                           name="last_changed" placeholder="Enter Last Changed">
                                                </div>
                                                <div class="dropdown_bottom">
                                                    <button class="btn light_green_btn apply-filter" type="submit">Apply
                                                        Filter
                                                    </button>
                                                    <button type="button" class="btn cancel_btn"
                                                            data-bs-dismiss="dropdown">Cancel
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                    <!-- Create Button -->
                                    <button type="button" class="btn dark_green_btn" data-bs-toggle="modal" data-bs-target="#create-filter"><i class="fa-solid fa-square-plus"></i>Create
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="accounts-datatables table table-row-bordered gy-5 custom_sizing">
                                <thead>
                                <tr>
                                    <th>SR#</th>
                                    <th>Account Name</th>
                                    <th>Country</th>
                                    <th>Sales Type</th>
                                    <th>Account Manager</th>
                                    <th>Last Changed At</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="modal fade create-filter create" id="create-filter" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLongTitle">Create Accounts/Golf Course</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="createAccountForm" action="{{route('accounts.store')}}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <div class="form-group">
                            <label for="manufLoc" class="form-label">Manuf Loc<span class="required-star">*</span></label>
                            <div class="radio-group checkbox-group">
                                <label><input id="New Zealand" class="type" type="radio" name="manufLoc" value="4"> New Zealand</label>
                                <label><input id="Australia" class="type" type="radio" name="manufLoc" value="3"> Australia</label>
                                <label><input id="United States" class="type" type="radio" name="manufLoc" value="5">United States</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="accountNameEdit">Account Name<span class="required-star">*</span></label>
                            <input type="text" class="form-control" id="accountNameEdit" name="account_name" placeholder="Enter account name" required>
                        </div>
                        <div class="form-group">
                            <label for="location">Location<span class="required-star">*</span></label>
                            <input type="text" class="form-control locationInput" id="locationEdit" name="location" placeholder="Enter location" required>
                            <input type="hidden" name="latitude" class="latitude" value="">
                            <input type="hidden" name="longitude" class="longitude" value="">
                            <input type="hidden" name="city" class="city" value="">
                            <input type="hidden" name="state" class="state" value="">
                            <input type="hidden" name="zip_code" class="zip" value="">
                        </div>
                        <div class="form-group">
                            <label for="countryEdit">Country<span class="required-star">*</span></label>
                            <select class="form-control country" id="countryEdit" name="country" required>
                                <option value="" disabled selected>Select a country</option>
                                @foreach($countries as $country)
                                    <option value="{{ $country->CntryCd }}">{{ $country->CntryNm }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="phone">Phone<span class="required-star">*</span></label>
                            <input type="text" class="form-control contact_number" name="phone"
                                   placeholder="(+123) 4568 7890 8520" required>
                            <div class="alert-info"></div>
                        </div>
                        <div class="form-group">
                            <label for="representative">Account Managers<span class="required-star">*</span></label>
                            <select class="form-control" id="representative" name="acctMgrId" required>
                                <option value="" selected disabled>Select Account Managers</option>
                                @foreach($accountManagers as $accountManager)
                                    <option
                                        value="{{$accountManager->AcctMgrId??''}}">{{$accountManager->AcctMgrNm??''}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="salesTypeEdit">Sales Type<span class="required-star">*</span></label>
                            <select class="form-control" id="acctTypeEdit" name="acctType" required>
                                <option value="" disabled selected>Select Sales Type</option>
                                @foreach($acctTypes as $acctType)
                                    <option value="{{ $acctType->AcctTypeId }}">{{ $acctType->AcctTypeDsc }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="statusEdit">Status<span class="required-star">*</span></label>
                            <select class="form-control" id="statusEdit" name="status" required>
                                <option value="" disabled selected>Select Status</option>
                                @foreach($allStatus as $status)
                                    <option value="{{ $status->AcctStatId }}">{{ $status->AcctStatDsc }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="websiteEdit">Website Address<span class="required-star">*</span></label>
                            <input type="url" class="form-control" id="websiteEdit" name="website"
                                   placeholder="Enter website URL" required>
                        </div>
                        <div class="form-group">
                            <label for="shippingOptionEdit">Default Shipping Option<span class="required-star">*</span></label>
                            <select class="form-control" id="shippingOptionEdit" name="shipVia" required>
                                <option value="" disabled selected>Select Shipping Option</option>
                                @foreach($shipVias as $shipVia)
                                    <option value="{{ $shipVia->ShipViaId }}">{{ $shipVia->ShipViaDsc }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="notesEdit">Notes / Comments</label>
                            <textarea class="form-control" id="notesEdit" name="comment" rows="3"
                                      placeholder="Enter any notes or comments"></textarea>
                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn light_green_btn">Create</button>
                            <button type="button" class="btn cancel_btn" data-bs-dismiss="modal">Close</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade create-filter payment-method" id="payment-method" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLongTitle">Payment Methods</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- Customer Creation Form (shown when no Stax customer ID) -->
                    <div id="customer-creation-section-stax" style="display: none;">
                        <h4>Create Stax Customer First</h4>
                        <p>This account doesn't have a Stax customer. Please create one first:</p>
                        <form id="customer-creation-form" method="POST"
                              action="{{ route('accounts.create-stax-customer') }}">
                            @csrf
                            <input type="hidden" name="account_id" id="customer_account_id" class="customer_account_id" value="">
                            <input type="hidden" name="account_name" id="account_name" value="">
                            <div class="form-group">
                                <label for="firstName">First Name<span class="required-star">*</span></label>
                                <input type="text" class="form-control" id="firstName" name="first_name"
                                       placeholder="Enter first name" required>
                            </div>
                            <div class="form-group">
                                <label for="lastName">Last Name<span class="required-star">*</span></label>
                                <input type="text" class="form-control" id="lastName" name="last_name"
                                       placeholder="Enter last name" required>
                            </div>
                            <div class="form-group">
                                <label for="email">Email<span class="required-star">*</span></label>
                                <input type="text" class="form-control" id="email" name="email"
                                       placeholder="Enter email" required>
                            </div>
                            <div class="form-group">
                                <label for="phone">Phone<span class="required-star">*</span></label>
                                <input type="text" class="form-control contact_number" name="phone"
                                       placeholder="(+123) 4568 7890 8520" required>
                                <div class="alert-info"></div>
                            </div>
                            <div class="form-group">
                                <label for="location">Location<span class="required-star">*</span></label>
                                <input type="text" class="form-control locationInput" id="locationEdit" name="location" placeholder="Enter location" required>
                                <input type="hidden" name="latitude" class="latitude" value="">
                                <input type="hidden" name="longitude" class="longitude" value="">
                                <input type="hidden" name="city" class="city" value="">
                                <input type="hidden" name="state" class="state" value="">
                                <input type="hidden" name="zip_code" class="zip" value="">
                            </div>
                            <div class="form-group">
                                <label for="countryEdit">Country<span class="required-star">*</span></label>
                                <select class="form-control country" id="countrySelect" name="country" required>
                                    <option value="" disabled selected>Select a country</option>
                                    @foreach($countries as $country)
                                        <option value="{{ $country->CntryCd }}">{{ $country->CntryNm }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="modal-footer">
                                <button type="submit" class="btn light_green_btn">Create Stax Customer</button>
                            </div>
                        </form>
                    </div>

                    <div id="customer-creation-section-stripe" style="display: none;">
                        <h4>Create Stripe Customer First</h4>
                        <p>This account doesn't have a Stripe customer. Please create one first:</p>
                        <form id="customer-creation-form" method="POST" action="{{ route('accounts.create-stripe-customer') }}">
                            @csrf
                            <input type="hidden" name="account_id" id="customer_account_id" class="customer_account_id" value="">
                            <input type="hidden" name="account_name" id="account_name" value="">
                            <div class="form-group">
                                <label for="firstName">First Name<span class="required-star">*</span></label>
                                <input type="text" class="form-control" id="firstName" name="first_name"
                                       placeholder="Enter first name" required>
                            </div>
                            <div class="form-group">
                                <label for="lastName">Last Name<span class="required-star">*</span></label>
                                <input type="text" class="form-control" id="lastName" name="last_name"
                                       placeholder="Enter last name" required>
                            </div>
                            <div class="form-group">
                                <label for="email">Email<span class="required-star">*</span></label>
                                <input type="text" class="form-control" id="email" name="email"
                                       placeholder="Enter email" required>
                            </div>
                            <div class="form-group">
                                <label for="phone">Phone<span class="required-star">*</span></label>
                                <input type="text" class="form-control contact_number" name="phone"
                                       placeholder="(+123) 4568 7890 8520" required>
                                <div class="alert-info"></div>
                            </div>
                            <div class="form-group">
                                <label for="location">Location<span class="required-star">*</span></label>
                                <input type="text" class="form-control locationInput" id="locationEdit" name="location" placeholder="Enter location" required>
                                <input type="hidden" name="latitude" class="latitude" value="">
                                <input type="hidden" name="longitude" class="longitude" value="">
                                <input type="hidden" name="city" class="city" value="">
                                <input type="hidden" name="state" class="state" value="">
                                <input type="hidden" name="zip_code" class="zip" value="">
                            </div>
                            <div class="form-group">
                                <label for="countryEdit">Country<span class="required-star">*</span></label>
                                <select class="form-control country" id="countrySelect" name="country" required>
                                    <option value="" disabled selected>Select a country</option>
                                    @foreach($countries as $country)
                                        <option value="{{ $country->CntryCd }}">{{ $country->CntryNm }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="modal-footer">
                                <button type="submit" class="btn light_green_btn">Create Stripe Customer</button>
                            </div>
                        </form>
                    </div>

                    <!-- Payment Method Form (shown when Stax customer exists) -->
                    <div id="payment-method-section">
                        <!-- Stripe Test Card Notice -->
                        <div id="stripe-test-notice" style="display: none; background: #e3f2fd; border: 1px solid #2196f3; padding: 10px; margin-bottom: 15px; border-radius: 4px;">
                            <strong>Stripe Test Mode:</strong> Use test card numbers like:
                            <ul style="margin: 5px 0; padding-left: 20px;">
                                <li><strong>4242 4242 4242 4242</strong> - Visa</li>
                                <li><strong>5555 5555 5555 4444</strong> - Mastercard</li>
                                <li><strong>3782 822463 10005</strong> - American Express</li>
                            </ul>
                            <small>Use any future expiry date and any 3-4 digit CVV.</small>
                        </div>

                        <form id="payment-method-form" method="POST"
                              action="{{ route('accounts.payment-methods.add') }}" enctype="multipart/form-data">
                            @csrf
                            <input type="hidden" name="account_id" id="payment_account_id" class="payment_account_id" value="">
                            <input type="hidden" name="gateway" class="gateway">
                            <div class="form-group">
                                <input class="form-control card_number" required="" type="text" name="card_number"
                                       id="card_number" placeholder="Card Number" minlength="16" maxlength="19"
                                       value="">
                            </div>
                            <div class="name_cvv_wrapper">
                                <div class="form-group">
                                    <input class="form-control card_holder_name" type="text" placeholder="Name"
                                           name="card_holder_name" id="card_holder_name" required="" value="">
                                </div>
                                <div class="form-group error">
                                    <input class="form-control card_cvv_number" type="text" placeholder="CVV"
                                           name="card_cvv_number" id="card_cvv_number" maxlength="4" minlength="3"
                                           required="" value="">
                                </div>
                                <div class="form-group error">
                                    <input class="form-control card_expiry" placeholder="MM/YY" name="card_expiry"
                                           id="card_expiry" required="" value="">
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="submit" class="btn light_green_btn">Add Card</button>
                            </div>
                        </form>
                        <div class="saved_payment_method_container">
                            <h3>Saved Payment Methods</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="edit_course">

    </div>

@endsection

@push('js')

    <script>
        $(document).ready(function () {
            var htmlContent = '<div class="dt-layout-cell dt-layout-start"><div class="dt-length"><label for="dt-length-0">Show</label><select aria-controls="DataTables_Table_0" class="dt-input" id="dt-length-0"><option value="10">10</option><option value="25">25</option><option value="50">50</option><option value="-1">All</option></select><label for="dt-length-0">entries</label></div></div>';
            document.querySelector('.side_fields.custom_entries').insertAdjacentHTML('afterbegin', htmlContent);
            var table = $('.accounts-datatables').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{{ route('accounts.index') }}',
                    type: 'GET',
                    data: function (d) {
                        d.country = $('#country').val();
                        d.salesType = $('#sales-type').val();
                        d.lastChanged = $('#last-changed').val();
                    },
                    dataSrc: 'data'
                },
                columns: [
                    {data: null, name: null, defaultContent: '', orderable: false},
                    {data: 'name', name: 'name', orderable: true},
                    {data: 'country', name: 'country', orderable: true},
                    {data: 'sales_type', name: 'sales_type', orderable: false},
                    {data: 'account_manager', name: 'account_manager', orderable: false},
                    {data: 'last_changed', name: 'last_changed', orderable: true},
                    {data: 'status', name: 'status', orderable: false},
                    {data: 'action', name: 'action', orderable: false, searchable: false}
                ],
                order: [[1, 'desc']], // Sort by Name descending
                pageLength: 10,
                lengthChange: false,
                ordering: false, // ✅ Allow column-based sorting
                searching: true,
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(0)').html(dataIndex + 1);
                },
                initComplete: function () {
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }
            });
            document.querySelector('#dt-length-0').addEventListener('change', function () {
                var selectedValue = this.value;
                if (selectedValue == "-1") {
                    table.page.len(-1).draw(); // Show all records
                } else {
                    table.page.len(parseInt(selectedValue)).draw(); // Set page length to selected value
                }
            });
            $(document).on("input", '.searchinput', function () {
                var searchValue = $(this).val();
                table.search(searchValue).draw();
            });
            $(".filter-form").submit(function (e) {
                e.preventDefault();
                table.draw();
            });
        });
    </script>


    <script>
        $(document).ready(function () {
            $("#createAccountForm").validate({
                rules: {
                    accountName: {
                        required: true,
                    },
                    country: {
                        required: true
                    },
                    representative: {
                        required: true
                    },
                    status: {
                        required: true
                    },
                    website: {
                        required: true,
                    },
                    shipping_option: {
                        required: true
                    }
                },
                messages: {
                    accountName: {
                        required: "Account name is required.",
                    },
                    country: "Please select a country.",
                    representative: "Please select a representative.",
                    status: "Please select a status.",
                    website: {
                        required: "Please enter a website.",
                    },
                    shipping_option: "Please enter a shipping option."
                },
                submitHandler: function (form) {
                    form.submit();
                }
            });
        });

        $(document).on('click', '.edit_account_button', function () {
            var accountId = $(this).attr('account_id');

            var url = '{{ route('accounts.edit', ':accountId') }}'.replace(':accountId', accountId);

            $.ajax({
                url: url,
                method: 'GET',
                success: function (response) {
                    $('.edit_course').html(response);
                    $('.edit-course-modal').modal('show');

                    initializePhoneInput();
                    initAutocomplete();
                },
                error: function (xhr, status, error) {
                    $('.edit_course').html('');
                    $('.edit-course-modal').modal('hide');
                }
            });
        });

        $('#editAccountForm').on('submit', function (e) {
            e.preventDefault();

            $.ajax({
                url: '{{ route('accounts.update', ':accountId') }}'.replace(':accountId', $('#accountNameEdit').val()),
                method: 'POST',
                data: $(this).serialize(),
                success: function (data) {
                    if (data.success) {
                        $('#edit-filter').modal('hide');
                        alert('Account updated successfully!');
                    } else {
                        alert('Error updating the account');
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error:', error);
                }
            });
        });

        // Payment Methods functionality
        $(document).on('click', '.payments_modal', function () {
            var accountId = $(this).attr('account_id');
            $('.payment_account_id').val(accountId);
            $('.customer_account_id').val(accountId);
            var manufId = $(this).attr('maunf_id');
            if (manufId === "3" || manufId === "4") {
                var gateway = 'Stripe';
                checkCustomer(accountId,gateway);
            } else {
                var gateway = 'Stax';
                checkCustomer(accountId,gateway);
            }
        });

        function checkCustomer(accountId,geteway) {
            $.ajax({
                url: '{{ route('accounts.check-customer') }}',
                method: 'POST',
                data: {
                    account_id: accountId,
                    geteway: geteway,
                    _token: '{{ csrf_token() }}'
                },
                success: function (response) {
                    if(response.gateway === "Stripe"){
                        if (response.has_stripe_customer) {
                            // Show payment methods section
                            $('#customer-creation-section-stripe').hide();
                            $('#customer-creation-section-stax').hide();
                            $('#payment-method-section').show();
                            $('.gateway').val('Stripe');
                            // Load existing payment methods
                            loadPaymentMethods(accountId,response.gateway);
                        } else {
                            // Show customer creation section
                            $('#payment-method-section').hide();
                            $('#customer-creation-section-stax').hide();
                            $('#customer-creation-section-stripe').show();
                            // Pre-fill account name if available
                            if (response.account_name) {
                                $('input[name="account_name"]').val(response.account_name);
                            }
                        }
                    }else if (response.gateway === "Stax"){
                        if (response.has_stax_customer) {
                            // Show payment methods section
                            $('#customer-creation-section-stax').hide();
                            $('#customer-creation-section-stripe').hide();
                            $('#payment-method-section').show();
                            $('.gateway').val('Stax');
                            // Load existing payment methods
                            loadPaymentMethods(accountId,response.gateway);
                        }else {
                            // Show customer creation section
                            $('#payment-method-section').hide();
                            $('#customer-creation-section-stripe').hide();
                            $('#customer-creation-section-stax').show();
                            // Pre-fill account name if available
                            if (response.account_name) {
                                $('input[name="account_name"]').val(response.account_name);
                            }
                        }
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error checking Stax customer:', error);
                    // Default to showing customer creation form
                    $('#payment-method-section').hide();
                    $('#customer-creation-section-stax').show();
                    $('#customer-creation-section-stripe').show();
                }
            });
        }

        function loadPaymentMethods(accountId,gateway) {
            $.ajax({
                url: '{{ route('accounts.payment-methods.get') }}',
                method: 'POST',
                data: {
                    gateway: gateway,
                    account_id: accountId,
                    _token: '{{ csrf_token() }}'
                },
                success: function (response) {
                    if (response.success) {
                        displayPaymentMethods(response.data);
                    } else {
                        console.error('Error loading payment methods:', response.error);
                        $('.saved_payment_method_container').html('<h3>Saved Payment Methods</h3><p>No payment methods found or error loading payment methods.</p>');
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error:', error);
                    $('.saved_payment_method_container').html('<h3>Saved Payment Methods</h3><p>Error loading payment methods.</p>');
                }
            });
        }

        function displayPaymentMethods(paymentMethods) {
            var container = $('.saved_payment_method_container');
            var html = '<h3>Saved Payment Methods</h3>';

            // Debug: Log the response to see the actual structure
            console.log('Payment Methods Response:', paymentMethods);

            // Handle different response structures
            var methods = paymentMethods;
            if (paymentMethods && paymentMethods.data) {
                methods = paymentMethods.data;
            }
            if (paymentMethods && paymentMethods.payment_methods) {
                methods = paymentMethods.payment_methods;
            }

            if (methods && methods.length > 0) {
                methods.forEach(function (method) {
                    // Check if this is a Stripe payment method (has 'card' object)
                    var isStripe = method.card && typeof method.card === 'object';

                    // Handle different field names for Stax and Stripe
                    var maskedNumber, cardType, holderName;

                    if (isStripe) {
                        // Stripe structure: method.card.last4, method.card.brand, etc.
                        maskedNumber = '**** **** **** ' + (method.card.last4 || '****');
                        cardType = method.card.brand || 'unknown';
                        holderName = (method.billing_details && method.billing_details.name) ||
                                   method.card.cardholder_name ||
                                   'N/A';
                    } else {
                        // Stax structure: method.last_four, method.card_type, etc.
                        maskedNumber = '**** **** **** ' + (
                            method.last_four ||
                            method.last4 ||
                            method.card_last_four ||
                            method.masked_number ||
                            '****'
                        );

                        cardType = method.card_type ||
                            method.type ||
                            method.brand ||
                            method.card_brand ||
                            'unknown';

                        holderName = method.card_holder_name ||
                            method.person_name ||
                            method.name ||
                            method.cardholder_name ||
                            'N/A';
                    }

                    // Handle expiry date in different formats
                    var expiry = 'N/A';

                    if (isStripe && method.card.exp_month && method.card.exp_year) {
                        // Stripe format: method.card.exp_month, method.card.exp_year
                        var month = method.card.exp_month.toString().padStart(2, '0');
                        var year = method.card.exp_year.toString();
                        if (year.length === 4) {
                            year = year.substring(2); // Convert 2025 to 25
                        }
                        expiry = month + ' / ' + year;
                    } else if (method.expiry_month && method.expiry_year) {
                        // Stax format: MM / YY
                        var month = method.expiry_month.toString().padStart(2, '0');
                        var year = method.expiry_year.toString();
                        if (year.length === 4) {
                            year = year.substring(2); // Convert 2025 to 25
                        }
                        expiry = month + ' / ' + year;
                    } else if (method.exp_month && method.exp_year) {
                        // Alternative format
                        var month = method.exp_month.toString().padStart(2, '0');
                        var year = method.exp_year.toString();
                        if (year.length === 4) {
                            year = year.substring(2); // Convert 2025 to 25
                        }
                        expiry = month + ' / ' + year;
                    } else if (method.card_exp) {
                        // If it's in MMYY format like "0427", convert to "04 / 27"
                        var exp = method.card_exp.toString();
                        if (exp.length === 4) {
                            expiry = exp.substring(0, 2) + ' / ' + exp.substring(2, 4);
                        } else if (exp.length === 6) {
                            // Handle MMYYYY format like "122025" -> "12 / 25"
                            expiry = exp.substring(0, 2) + ' / ' + exp.substring(4, 6);
                        } else {
                            expiry = method.card_exp;
                        }
                    } else if (method.expiry) {
                        var exp = method.expiry.toString();
                        if (exp.length === 6) {
                            // Handle MMYYYY format like "122025" -> "12 / 25"
                            expiry = exp.substring(0, 2) + ' / ' + exp.substring(4, 6);
                        } else if (exp.length === 4) {
                            // Handle MMYY format like "1225" -> "12 / 25"
                            expiry = exp.substring(0, 2) + ' / ' + exp.substring(2, 4);
                        } else {
                            expiry = method.expiry;
                        }
                    }

                    var paymentId = method.id || method.payment_method_id || method.token || 'unknown';

                    html += `
                        <div class="card" data-payment-id="${paymentId}">
                            <button type="button" class="btn cancel_btn delete_card_btn" onclick="deletePaymentMethod('${paymentId}')">
                                <i class="fa-solid fa-trash" aria-hidden="true"></i>Delete
                            </button>
                            <div class="top_label_wrapper">
                                <label class="lbl_card_number txt_white">Card Number</label>
                                <label class="lbl_card_type txt_white">${cardType}</label>
                            </div>
                            <div class="form-group">
                                <input class="form-control card_number" type="text" value="${maskedNumber}" disabled="">
                            </div>
                            <div class="name_cvv_wrapper">
                                <div class="form-group">
                                    <label class="txt_white">Name</label>
                                    <input class="form-control card_holder_name" type="text" value="${holderName}" disabled="">
                                </div>
                                <div class="form-group">
                                    <label class="txt_white">CVV</label>
                                    <input class="form-control card_cvv_number" type="text" value="***" disabled="">
                                </div>
                                <div class="form-group">
                                    <label class="txt_white">Expiry Date</label>
                                    <input class="form-control card_expiry" value="${expiry}" disabled="">
                                </div>
                            </div>
                        </div>
                    `;
                });
            } else {
                html += '<p>No payment methods found.</p>';
            }

            container.html(html);
        }

        function deletePaymentMethod(paymentMethodId) {
            if (confirm('Are you sure you want to delete this payment method?')) {
                var accountId = $('.payment_account_id').val();

                $.ajax({
                    url: '{{ route('accounts.payment-methods.delete') }}',
                    method: 'DELETE',
                    data: {
                        account_id: accountId,
                        payment_method_id: paymentMethodId,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function (response) {
                        if (response.success) {
                            // Remove the card from display
                            $(`[data-payment-id="${paymentMethodId}"]`).remove();
                            alert('Payment method deleted successfully!');
                        } else {
                            alert('Error deleting payment method: ' + (response.error.message || 'Unknown error'));
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('Error:', error);
                        alert('Error deleting payment method');
                    }
                });
            }
        }

        // Format card number input
        $('#card_number').on('input', function () {
            var value = $(this).val().replace(/\s+/g, '').replace(/[^0-9]/gi, '');
            var formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            $(this).val(formattedValue);
        });

        // Format expiry date input
        $('#card_expiry').on('input', function () {
            var value = $(this).val().replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '/' + value.substring(2, 4);
            }
            $(this).val(value);
        });

        // Handle customer creation form submission
        $('#customer-creation-form').on('submit', function (e) {
            e.preventDefault();
            $.ajax({
                url: $(this).attr('action'),
                method: 'POST',
                data: $(this).serialize(),
                success: function (response) {
                    if (response.success) {
                        $('#payment-method').modal('hide');
                        Swal.fire({
                            icon: 'success',
                            title: 'STAX Customer!',
                            text: 'Stax customer created successfully',
                            confirmButtonText: 'OK'
                        }).then(function () {
                            location.reload();
                        });
                    } else {
                        alert('Error creating Stax customer: ' + (response.message || 'Unknown error'));
                        location.reload();
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error:', error);
                    var errorMessage = 'Error creating Stax customer';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage += ': ' + xhr.responseJSON.message;
                    }
                    alert(errorMessage);
                }
            });
        });
    </script>

@endpush
