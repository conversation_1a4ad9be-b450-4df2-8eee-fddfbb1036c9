@extends('website.layout.master')
@section('content')
    <section class="banner">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="main_banner">
                        <h1>Checkout</h1>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="checkout_section">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="shopping_box">
                        <h6>Contact Info</h6><h6>Address</h6><h6>Payment</h6>
                    </div>
                </div>
                <div class="col-md-8 col-sm-7">
                    <form class="stepper_form" action="{{route('order-form-submit')}}" method="post">
                        @csrf
                        <div class="form_steps">
                            <!-- Order Type Toggle -->
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <div class="order-type-toggle">
                                        <h6 class="mb-3">Ship To:</h6>
                                        <div class="toggle-container">
                                            <label class="toggle-option">
                                                <input type="radio" name="order_type" value="fitter" checked>
                                                <span class="toggle-label">Fitter</span>
                                            </label>
                                            <label class="toggle-option">
                                                <input type="radio" name="order_type" value="customer">
                                                <span class="toggle-label">Customer</span>
                                            </label>
                                        </div>


                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="first_name">First Name</label>
                                        <input id="first_name" type="text" name="first_name" placeholder="First Name" class="form-control" value="{{ Auth::check() ? Auth::user()->fitter->FtrNmFirst ?? '' : '' }}" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="last_name">Last Name</label>
                                        <input id="last_name" type="text" name="last_name" placeholder="Last Name" class="form-control" value="{{ Auth::check() ? Auth::user()->fitter->FtrNmLast ?? '' : '' }}" readonly>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="email">Email</label>
                                        <input id="email" type="email" name="email" placeholder="Email" class="form-control" value="{{ Auth::check() ? Auth::user()->email ?? '' : '' }}" readonly>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="phone_number">Phone Number</label>
                                        <input id="phone_number" type="tel" name="phone_number" placeholder="Phone Number" class="form-control" value="{{ Auth::check() ? Auth::user()->fitter->CellPhone ?? '' : '' }}" readonly autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form_steps">
                            <div class="address_box">
                                <div class="address_content_box" id="account-address">
                                    <div class="address_content">
                                        <label class="custom-radio">
                                            <input type="radio" name="address" value="account" id="account_address_radio" checked>
                                            <span class="custom-radio-mark"></span>
                                        </label>
                                        <div class="title_box">
                                            <h6>{{Auth::user()->fitter->account->AcctNm??''}}</h6>
                                            <p>{{Auth::user()->fitter->account->location??''}}</p>
                                            <div class="contact">
                                                <p>Contact - </p>
                                                <span>{{Auth::user()->fitter->account->phoneNum??''}}</span>
                                            </div>
                                        </div>
                                        <div class="address_box_tag">
                                            <span>Account</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="address_content_box" id="customer-address">
                                    <div class="address_content">
                                        <label class="custom-radio">
                                            <input type="radio" name="address" value="customer" id="customer_address_radio">
                                            <span class="custom-radio"></span>
                                        </label>
                                        <div class="title_box">
                                            <h6>{{$customer->DfltNameOnTag??''}}</h6>
                                            <p>{{$order->ShipToAddr??''}}</p>
                                            <div class="contact">
                                                <p>Contact- </p>
                                                <span>{{$customer->PhnNum??''}}</span>
                                            </div>
                                        </div>
                                        <div class="address_box_tag">
                                            <span>Customer</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="new_address">
                                <button type="button" class="add_address">
                                    <i class="fa fa-plus"></i> Add New Address
                                </button>
                                <div class="shipping-address-form shippment_form">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <h6>New Shipping Address</h6>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label for="OptBllAddr">Street Address</label>
                                                <input id="OptBllAddr" name="OptBllAddr" type="text" placeholder="Select Address" class="form-control" >
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="OptBillCityNm">City</label>
                                                <input id="OptBillCityNm" type="text" name="OptBillCityNm" placeholder="City" class="form-control" >
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="OptBillStateCd">State</label>
                                                <input id="OptBillStateCd" type="text" name="OptBillStateCd" placeholder="State" class="form-control" >
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="OptBillPostalCd">Zip Code</label>
                                                <input id="OptBillPostalCd" type="text" name="OptBillPostalCd" placeholder="Zip Code" class="form-control" >
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form_steps">
                            <div class="address_box payment" id="payment-methods-container">
                                <!-- Payment methods will be loaded dynamically here -->
                                <div class="loading-payment-methods" style="text-align: center; padding: 20px;">
                                    <p>Loading payment methods...</p>
                                </div>
                            </div>
                        </div>
                        <div class="stepper_button">
                            <div class="light_btn">
                                <button type="button" class="btn btn-primary btn-primary_green" id="prevBtn" disabled="">
                                    <i class="fas fa-arrow-left"></i>
                                </button>
                            </div>
                            <div class="light_btn">
                                <button type="button" class="btn btn-primary btn-primary_green" id="nextBtn">
                                    Next
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="col-md-4 col-sm-5">
                    <div class="summary">
                        <div class="summary_box">
                            <div class="detail_content">
                                <h6>Order Summary</h6>
                                <div class="detail_value sub_total">
                                    <p>Sub Total</p>
                                    <p>${{ isset($coupon) ? number_format($coupon['original_total'], 2) : number_format($order->TtlAmt ?? 0, 2) }}</p>
                                </div>
                                <div class="detail_value discount">
                                    <p>Discount</p>
                                    <p>{{ isset($coupon) ? $coupon['discount_percentage'] : 0 }}%</p>
                                </div>
                                <div class="detail_value shipping">
                                    <p>Shipping</p>
                                    <p class="free">Free</p>
                                </div>
                                <div class="detail_value coupon_applied">
                                    <p>{{ isset($coupon) ? $coupon['type'] . ' Applied' : 'Coupon Applied' }}</p>
                                    <p>${{ isset($coupon) ? number_format($coupon['discount_amount'], 2) : '0.00' }}</p>
                                </div>
                            </div>
                            <div class="total_content">
                                <div class="total_value">
                                    <p>TOTAL</p>
                                    <p>${{ isset($coupon) ? number_format($coupon['new_total'], 2) : number_format($order->TtlAmt ?? 0, 2) }}</p>
                                </div>
                                <div class="coupon_field">
                                    <input type="text" placeholder="Coupon Code" name="coupon_code" id="coupon_code" value="{{ isset($coupon) ? $coupon['code'] : '' }}" readonly>
                                    <i class="fa-regular fa-tag"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('css')
<style>
    .loading-payment-methods, .no-payment-methods, .error-payment-methods {
        background: #f8f9fa;
        border: 1px dashed #dee2e6;
        border-radius: 8px;
        color: #6c757d;
        font-style: italic;
    }

    .loading-payment-methods {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    .error-payment-methods {
        background: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }

    .address_payment_box {
        transition: all 0.3s ease;
    }

    .address_payment_box:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    /* Order Type Toggle Styles */
    .order-type-toggle {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }

    .toggle-container {
        display: flex;
        gap: 20px;
        align-items: center;
    }

    .toggle-option {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 10px 20px;
        border: 2px solid #dee2e6;
        border-radius: 25px;
        background: white;
        transition: all 0.3s ease;
        margin: 0;
    }

    .toggle-option:hover {
        border-color: #007bff;
        background: #f8f9ff;
    }

    .toggle-option input[type="radio"] {
        margin-right: 8px;
        margin: 0;
    }

    .toggle-option input[type="radio"]:checked + .toggle-label {
        color: #007bff;
        font-weight: 600;
    }

    .toggle-option:has(input[type="radio"]:checked) {
        border-color: #007bff;
        background: #e7f3ff;
    }

    .toggle-label {
        font-size: 14px;
        font-weight: 500;
        margin-left: 8px;
        transition: all 0.3s ease;
    }
</style>
@endpush

@push('js')
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const steps = document.querySelectorAll(".form_steps");
            const headings = document.querySelectorAll(".shopping_box h6");
            const nextBtn = document.getElementById("nextBtn");
            const prevBtn = document.getElementById("prevBtn");
            let currentStep = 0;

            function showStep(step) {
                steps.forEach((formStep, index) => {
                    formStep.classList.toggle("active", index === step);
                    formStep.style.display = index === step ? "block" : "none";
                });

                headings.forEach((heading, index) => {
                    heading.classList.toggle("active", index === step);
                });

                prevBtn.disabled = step === 0;
                nextBtn.textContent = step === steps.length - 1 ? "Place Order" : "Next";
            }

            function validateStep() {
                const inputs = steps[currentStep].querySelectorAll("[required]");
                let isValid = true;

                inputs.forEach((input) => {
                    if (!input.value.trim()) {
                        input.classList.add("is-invalid");
                        isValid = false;
                    } else {
                        input.classList.remove("is-invalid");
                    }
                });

                return isValid;
            }

            nextBtn.addEventListener("click", () => {
                if (currentStep < steps.length - 1) {
                    if (validateStep()) {
                        currentStep++;
                        showStep(currentStep);
                    }
                } else {
                    if (validateStep()) {
                        document.querySelector(".stepper_form").submit();
                    }
                }
            });

            prevBtn.addEventListener("click", () => {
                if (currentStep > 0) {
                    currentStep--;
                    showStep(currentStep);
                }
            });

            showStep(currentStep);
        });


        $(document).ready(function(){
            // Load payment methods when page loads
            loadPaymentMethods();
            // Handle order type toggle
            $('input[name="order_type"]').change(function(){
                var orderType = $(this).val();
                if (orderType === 'fitter') {
                    populateFitterData();
                    updateAddressSelection('account');
                } else if (orderType === 'customer') {
                    populateCustomerData();
                    updateAddressSelection('customer');
                }
            });
            $(".add_address").click(function(){
                $(".shippment_form").show();
                $(".add_address").hide();
                updateAddressSelection('');
            });
        });

        function populateFitterData() {
            // Populate with fitter data from Auth::user()
            @if(Auth::check() && Auth::user()->fitter)
                $('#first_name').val('{{ Auth::user()->fitter->FtrNmFirst ?? "" }}');
                $('#last_name').val('{{ Auth::user()->fitter->FtrNmLast ?? "" }}');
                $('#email').val('{{ Auth::user()->email ?? "" }}');
                $('#phone_number').val('{{ Auth::user()->fitter->CellPhone ?? "" }}');
            @else
                $('#first_name').val('');
                $('#last_name').val('');
                $('#email').val('');
                $('#phone_number').val('');
            @endif
        }

        function populateCustomerData() {
            // Populate with customer data from session/controller
            @if($customer != null)
                $('#first_name').val('{{ $customer->FirstNm ?? "" }}');
                $('#last_name').val('{{ $customer->LastNm ?? "" }}');
                $('#email').val('{{ $customer->EMailAddr ?? "" }}');
                $('#phone_number').val('{{ $customer->PhnNum ?? "" }}');
            @else
                // Clear fields if no customer data available
                $('#first_name').val('');
                $('#last_name').val('');
                $('#email').val('');
                $('#phone_number').val('');
            @endif
        }

        function updateAddressSelection(addressType) {
            // Uncheck all address radio buttons first
            $('input[name="address"]').prop('checked', false);

            // Check the appropriate address based on order type
            if (addressType === 'account') {
                $('#account_address_radio').prop('checked', true);
            } else if (addressType === 'customer') {
                $('#customer_address_radio').prop('checked', true);
            }else{
                $('#account_address_radio').prop('checked', false);
                $('#customer_address_radio').prop('checked', false);
            }
        }

        function loadPaymentMethods() {
            var accountId = `{{Auth::user()->fitter->AcctId??''}}`;

            if (!accountId) {
                $('#payment-methods-container').html('<div class="no-payment-methods" style="text-align: center; padding: 20px;"><p>Please login to view payment methods.</p></div>');
                return;
            }

            $.ajax({
                url: '{{ route('accounts.payment-methods.get') }}',
                method: 'POST',
                data: {
                    account_id: accountId,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success && response.data && response.data.length > 0) {
                        displayPaymentMethods(response.data);
                    } else {
                        $('#payment-methods-container').html('<div class="no-payment-methods" style="text-align: center; padding: 20px;"><p>No payment methods found. Please add a payment method.</p></div>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading payment methods:', error);
                    $('#payment-methods-container').html('<div class="error-payment-methods" style="text-align: center; padding: 20px;"><p>Error loading payment methods. Please try again.</p></div>');
                }
            });
        }

        function displayPaymentMethods(paymentMethods) {
            var html = '';

            paymentMethods.forEach(function(method, index) {
                // Check if this is a Stripe payment method (has 'card' object)
                var isStripe = method.card && typeof method.card === 'object';

                var cardType, lastFour, expiryMonth, expiryYear, expiry = '';

                if (isStripe) {
                    // Stripe structure
                    cardType = getCardType(method.card.brand || 'card');
                    lastFour = method.card.last4 || '****';
                    expiryMonth = method.card.exp_month || '';
                    expiryYear = method.card.exp_year || '';
                } else {
                    // Stax structure
                    cardType = getCardType(method.card_type || method.brand || 'card');
                    lastFour = method.last_four || method.card_last_four || '****';
                    expiryMonth = method.card_exp_month || method.exp_month || '';
                    expiryYear = method.card_exp_year || method.exp_year || '';
                }

                var cardImage = getCardImage(cardType);

                if (expiryMonth && expiryYear) {
                    expiry = String(expiryMonth).padStart(2, '0') + '/' + String(expiryYear).slice(-2);
                }

                html += `
                    <div class="address_payment_box" data-payment-id="${method.id}">
                        <label class="custom-card-radio">
                            <div class="address_payment">
                                <input type="radio" name="payment_method" value="${method.id}" ${index === 0 ? 'checked' : ''}>
                                <span class="custom-radio-mark"></span>
                                <div class="payment_info">
                                    <img class="img-fluid" src="${cardImage}" alt="${cardType}">
                                    <p>•••• ${lastFour}</p>
                                    ${expiry ? `<p class="payment_date">Expires ${expiry}</p>` : ''}
                                </div>
                            </div>
                        </label>
                    </div>
                `;
            });

            $('#payment-methods-container').html(html);
        }

        function getCardType(cardType) {
            if (!cardType) return 'card';

            cardType = cardType.toLowerCase();
            if (cardType.includes('visa')) return 'visa';
            if (cardType.includes('master')) return 'mastercard';
            if (cardType.includes('amex') || cardType.includes('american')) return 'amex';
            if (cardType.includes('discover')) return 'discover';

            return 'card';
        }

        function getCardImage(cardType) {
            var baseUrl = '{{asset('website')}}/assets/images/';

            switch(cardType) {
                case 'visa':
                    return baseUrl + 'visa.png';
                case 'mastercard':
                    return baseUrl + 'mastercard.svg';
                case 'amex':
                    return baseUrl + 'card-visa.svg'; // fallback to available card image
                case 'discover':
                    return baseUrl + 'card-visa.svg'; // fallback to available card image
                default:
                    return baseUrl + 'card-visa.svg'; // fallback to available card image
            }
        }
        initAutocomplete();
        function initAutocomplete() {
            var input = document.getElementById('OptBllAddr');
            var autocomplete = new google.maps.places.Autocomplete(input, {
                // types: ['address'],
                componentRestrictions: {country: ['au', 'us']}
            });
            autocomplete.addListener('place_changed', function () {
                var place = autocomplete.getPlace();
                var city, state, zip, country;
                for (var i = 0; i < place.address_components.length; i++) {
                    var component = place.address_components[i];
                    if (component.types.includes('locality')) {
                        city = component.long_name;
                    }
                    if (component.types.includes('administrative_area_level_1')) {
                        state = component.short_name;
                    }
                    if (component.types.includes('postal_code')) {
                        zip = component.long_name;
                    }
                    if (component.types.includes('country')) {
                        country = component.short_name;
                    }
                }
                $('#OptBillCityNm').val(city);
                $('#OptBillStateCd').val(state);
                $('#OptBillPostalCd').val(zip);
            });
        }
    </script>
@endpush

